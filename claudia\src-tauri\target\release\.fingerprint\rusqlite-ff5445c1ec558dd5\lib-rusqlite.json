{"rustc": 1842507548689473721, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 2662118639388351456, "path": 13718170166304641978, "deps": [[3056352129074654578, "hashlink", false, 10740749554724491678], [3666196340704888985, "smallvec", false, 14520583744597987552], [5510864063823219921, "fallible_streaming_iterator", false, 15726934246705453310], [7896293946984509699, "bitflags", false, 9975300548933407736], [12860549049674006569, "fallible_iterator", false, 14112370505845420281], [16675652872862304210, "libsqlite3_sys", false, 1953271284234226256]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rusqlite-ff5445c1ec558dd5\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}