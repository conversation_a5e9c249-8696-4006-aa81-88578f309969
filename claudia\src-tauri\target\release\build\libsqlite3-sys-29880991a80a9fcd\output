cargo:rerun-if-env-changed=LIBSQLITE3_SYS_USE_PKG_CONFIG
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1/sqlite3
cargo:rerun-if-changed=sqlite3/sqlite3.c
cargo:rerun-if-changed=sqlite3/wasm32-wasi-vfs.c
cargo:rerun-if-env-changed=SQLITE_MAX_VARIABLE_NUMBER
cargo:rerun-if-env-changed=SQLITE_MAX_EXPR_DEPTH
cargo:rerun-if-env-changed=SQLITE_MAX_COLUMN
cargo:rerun-if-env-changed=LIBSQLITE3_FLAGS
OUT_DIR = Some(C:\Users\<USER>\Desktop\cl\claudia\src-tauri\target\release\build\libsqlite3-sys-29880991a80a9fcd\out)
OPT_LEVEL = Some(z)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Desktop\cl\claudia\src-tauri\target\release\deps;C:\Users\<USER>\Desktop\cl\claudia\src-tauri\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Program Files (x86)\Incredibuild;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\vcpkg-2025.01.13;C:\Program Files\nodejs;C:\Program Files\Tesseract-OCR;C:\Program Files\Go\bin;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Docker\Docker\resources\bin;C:\maven-mvnd-1.0.2-windows-amd64\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Users\<USER>\Program Files\dotnet;C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools;C:\Users\<USER>\Downloads\w64devkit\bin;C:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\apache-maven-3.9.9\bin;C:\Users\<USER>\go\bin;C:\Program Files (x86)\Nmap;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
sqlite3.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.42.34433\atlmfc\lib\x64
cargo:rustc-link-lib=static=sqlite3
cargo:rustc-link-search=native=C:\Users\<USER>\Desktop\cl\claudia\src-tauri\target\release\build\libsqlite3-sys-29880991a80a9fcd\out
cargo:lib_dir=C:\Users\<USER>\Desktop\cl\claudia\src-tauri\target\release\build\libsqlite3-sys-29880991a80a9fcd\out
