{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2662118639388351456, "path": 1288308961366000512, "deps": [[442785307232013896, "build_script_build", false, 7810114614729308839], [3150220818285335163, "url", false, 7366155678605360034], [4143744114649553716, "raw_window_handle", false, 14934401824223199199], [7606335748176206944, "dpi", false, 5186755730130821963], [9010263965687315507, "http", false, 5736142074781997427], [9689903380558560274, "serde", false, 16240763126667200888], [10806645703491011684, "thiserror", false, 14954065874083962121], [11050281405049894993, "tauri_utils", false, 13255964311063383720], [13116089016666501665, "windows", false, 14437696574956694987], [15367738274754116744, "serde_json", false, 14503338534462317571], [16727543399706004146, "cookie", false, 2881109668009702843]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-1f18bfbbfdd21839\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}