{"rustc": 1842507548689473721, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 2662118639388351456, "path": 632669836025513150, "deps": [[778154619793643451, "hyper_util", false, 7152684944828692058], [784494742817713399, "tower_service", false, 10919824660295141671], [2883436298747778685, "pki_types", false, 10312138117464517757], [5907992341687085091, "webpki_roots", false, 14980106713416981609], [9010263965687315507, "http", false, 5736142074781997427], [9538054652646069845, "tokio", false, 7452362994327593081], [11895591994124935963, "tokio_rustls", false, 13872679831506678596], [11957360342995674422, "hyper", false, 13871368901690221053], [16400140949089969347, "rustls", false, 11238617066961353188]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-rustls-e2b4e4aaa70ec283\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}