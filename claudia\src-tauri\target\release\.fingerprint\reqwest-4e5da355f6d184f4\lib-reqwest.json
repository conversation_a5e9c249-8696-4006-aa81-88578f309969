{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 13447052689028050801, "path": 304012281195555478, "deps": [[40386456601120721, "percent_encoding", false, 1601839559144273945], [418947936956741439, "h2", false, 9603254592719282582], [778154619793643451, "hyper_util", false, 7152684944828692058], [784494742817713399, "tower_service", false, 10919824660295141671], [1288403060204016458, "tokio_util", false, 10991929984510853831], [1788832197870803419, "hyper_rustls", false, 15840857207458651249], [1906322745568073236, "pin_project_lite", false, 3059951252696025657], [2054153378684941554, "tower_http", false, 7500317543973772316], [2517136641825875337, "sync_wrapper", false, 14429031451087183564], [2883436298747778685, "rustls_pki_types", false, 10312138117464517757], [3150220818285335163, "url", false, 7366155678605360034], [5695049318159433696, "tower", false, 16519088602508127123], [5907992341687085091, "webpki_roots", false, 14980106713416981609], [5986029879202738730, "log", false, 3307635332154852194], [7620660491849607393, "futures_core", false, 12894734572691424698], [8298091525883606470, "cookie_store", false, 13476413193015006009], [9010263965687315507, "http", false, 5736142074781997427], [9538054652646069845, "tokio", false, 7452362994327593081], [9689903380558560274, "serde", false, 16240763126667200888], [10229185211513642314, "mime", false, 14822628542397372441], [10629569228670356391, "futures_util", false, 16376393732304720747], [11895591994124935963, "tokio_rustls", false, 13872679831506678596], [11957360342995674422, "hyper", false, 13871368901690221053], [12186126227181294540, "tokio_native_tls", false, 13417983596300245143], [13077212702700853852, "base64", false, 4472164941702080777], [14084095096285906100, "http_body", false, 8735516562944697805], [14564311161534545801, "encoding_rs", false, 11853950645030567299], [15367738274754116744, "serde_json", false, 14503338534462317571], [16066129441945555748, "bytes", false, 1732835555531081075], [16400140949089969347, "rustls", false, 11238617066961353188], [16542808166767769916, "serde_urlencoded", false, 5542596480399871863], [16727543399706004146, "cookie_crate", false, 2881109668009702843], [16785601910559813697, "native_tls_crate", false, 14259904508534042624], [16900715236047033623, "http_body_util", false, 1839031697386828523], [18273243456331255970, "hyper_tls", false, 17062005995060135450]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-4e5da355f6d184f4\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}