{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 4970587784845881345, "path": 13236182631312062850, "deps": [[5103565458935487, "futures_io", false, 7159503565502304813], [1811549171721445101, "futures_channel", false, 8349585590945733704], [7013762810557009322, "futures_sink", false, 5711511558395004792], [7620660491849607393, "futures_core", false, 12894734572691424698], [10629569228670356391, "futures_util", false, 16376393732304720747], [12779779637805422465, "futures_executor", false, 8967876024046866554], [16240732885093539806, "futures_task", false, 6236864016888530259]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-c56d28023446c562\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}