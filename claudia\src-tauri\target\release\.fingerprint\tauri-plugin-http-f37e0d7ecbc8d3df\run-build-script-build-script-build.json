{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 11679087657081756532], [13890802266741835355, "build_script_build", false, 11420061277011040009], [15441187897486245138, "build_script_build", false, 7168197907783078094]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-http-f37e0d7ecbc8d3df\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}