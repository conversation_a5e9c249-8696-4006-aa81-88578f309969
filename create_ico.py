#!/usr/bin/env python3
import struct

def create_simple_ico():
    # ICO header
    ico_header = struct.pack('<HHH', 0, 1, 1)  # Reserved, Type (1=ICO), Count
    
    # Directory entry for 16x16 icon
    dir_entry = struct.pack('<BBBBHHLL', 
                           16,    # Width
                           16,    # Height  
                           0,     # Color count
                           0,     # Reserved
                           1,     # Color planes
                           32,    # Bits per pixel
                           1024,  # Size of image data
                           22)    # Offset to image data
    
    # Simple 16x16 RGBA bitmap data (all blue pixels)
    bitmap_data = b'\x00\x00\xFF\xFF' * 256  # 16x16 pixels, BGRA format
    
    # Write ICO file
    with open('src-tauri/icons/icon.ico', 'wb') as f:
        f.write(ico_header)
        f.write(dir_entry)
        f.write(bitmap_data)
    
    print("Created simple ICO file")

if __name__ == "__main__":
    create_simple_ico()
