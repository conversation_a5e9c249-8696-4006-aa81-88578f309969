{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 2662118639388351456, "path": 5451450293158687294, "deps": [[1462335029370885857, "quick_xml", false, 9036993284723838107], [3334271191048661305, "windows_version", false, 7750783450748014395], [10806645703491011684, "thiserror", false, 14954065874083962121], [13116089016666501665, "windows", false, 14437696574956694987]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winrt-notification-0436cc9167b334e6\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}