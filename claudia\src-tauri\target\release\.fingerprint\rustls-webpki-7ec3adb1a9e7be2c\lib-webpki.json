{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 2662118639388351456, "path": 12590007092919164603, "deps": [[2883436298747778685, "pki_types", false, 10312138117464517757], [5491919304041016563, "ring", false, 14645251676311574149], [8995469080876806959, "untrusted", false, 9500961113043513655]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-webpki-7ec3adb1a9e7be2c\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}