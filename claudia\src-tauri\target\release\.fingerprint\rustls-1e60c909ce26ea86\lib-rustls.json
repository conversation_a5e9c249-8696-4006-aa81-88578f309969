{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 9722383079542159310, "path": 1749457146276060112, "deps": [[2883436298747778685, "pki_types", false, 10312138117464517757], [3722963349756955755, "once_cell", false, 3197273933547246981], [5491919304041016563, "ring", false, 14645251676311574149], [6528079939221783635, "zeroize", false, 12089119799770893317], [16400140949089969347, "build_script_build", false, 15978909154587643202], [17003143334332120809, "subtle", false, 15319165651477192278], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 792532390938793525]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-1e60c909ce26ea86\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}