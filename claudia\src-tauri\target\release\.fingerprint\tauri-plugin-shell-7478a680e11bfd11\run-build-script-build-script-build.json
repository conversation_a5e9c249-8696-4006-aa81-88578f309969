{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 11679087657081756532], [1582828171158827377, "build_script_build", false, 17634254975471190164]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-shell-7478a680e11bfd11\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}