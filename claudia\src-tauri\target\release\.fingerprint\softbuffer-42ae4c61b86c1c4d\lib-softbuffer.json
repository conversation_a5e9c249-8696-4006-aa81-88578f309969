{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2662118639388351456, "path": 4872535670476380077, "deps": [[376837177317575824, "build_script_build", false, 12049930090940719886], [4143744114649553716, "raw_window_handle", false, 14934401824223199199], [5986029879202738730, "log", false, 3307635332154852194], [10281541584571964250, "windows_sys", false, 726872013984463490]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-42ae4c61b86c1c4d\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}