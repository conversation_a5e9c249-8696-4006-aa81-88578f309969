{"rustc": 1842507548689473721, "features": "[\"default\", \"public_suffix\", \"serde\", \"serde_json\"]", "declared_features": "[\"default\", \"log_secure_cookie_values\", \"preserve_order\", \"public_suffix\", \"serde\", \"serde_json\", \"serde_ron\", \"wasm-bindgen\"]", "target": 8140962409157740669, "profile": 2662118639388351456, "path": 17266973371716140037, "deps": [[505596520502798227, "publicsuffix", false, 9003467569211362854], [3150220818285335163, "url", false, 7366155678605360034], [5986029879202738730, "log", false, 3307635332154852194], [6376232718484714452, "idna", false, 1344185123939825265], [9689903380558560274, "serde", false, 16240763126667200888], [11763018104473073732, "document_features", false, 10578246348076707558], [12409575957772518135, "time", false, 9964476927266573273], [15367738274754116744, "serde_json", false, 14503338534462317571], [16257276029081467297, "serde_derive", false, 15133432834485033226], [16727543399706004146, "cookie", false, 2881109668009702843]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\cookie_store-1d5778093f0ea600\\dep-lib-cookie_store", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}