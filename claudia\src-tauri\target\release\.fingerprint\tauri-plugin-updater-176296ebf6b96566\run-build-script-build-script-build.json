{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 11679087657081756532], [18440762029541581206, "build_script_build", false, 10657526079411265422]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-updater-176296ebf6b96566\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}