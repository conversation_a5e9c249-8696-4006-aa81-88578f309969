{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2662118639388351456, "path": 442901360617320951, "deps": [[5491919304041016563, "build_script_build", false, 17954291951869172276], [8995469080876806959, "untrusted", false, 9500961113043513655], [9920160576179037441, "getrandom", false, 1070924662586783518], [10411997081178400487, "cfg_if", false, 15758687479998905678]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ring-4fb07c670d035bf0\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}