{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2662118639388351456, "path": 9958066978812107341, "deps": [[784494742817713399, "tower_service", false, 10919824660295141671], [1906322745568073236, "pin_project_lite", false, 3059951252696025657], [4121350475192885151, "iri_string", false, 18397265017199600874], [5695049318159433696, "tower", false, 16519088602508127123], [7712452662827335977, "tower_layer", false, 1284136098434458383], [7896293946984509699, "bitflags", false, 9975300548933407736], [9010263965687315507, "http", false, 5736142074781997427], [10629569228670356391, "futures_util", false, 16376393732304720747], [14084095096285906100, "http_body", false, 8735516562944697805], [16066129441945555748, "bytes", false, 1732835555531081075]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-4cedf2991fb8b04c\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}