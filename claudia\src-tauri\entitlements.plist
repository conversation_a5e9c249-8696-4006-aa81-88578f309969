<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Allow app to be run from Homebrew installation -->
    <key>com.apple.security.app-sandbox</key>
    <false/>
    
    <!-- Network access for Claude API and other services -->
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.network.server</key>
    <true/>
    
    <!-- File system access -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>
    
    <!-- Allow spawning subprocesses (needed for shell commands) -->
    <key>com.apple.security.inherit</key>
    <true/>
    
    <!-- Allow automation for Apple Events -->
    <key>com.apple.security.automation.apple-events</key>
    <true/>
    
    <!-- Camera and microphone if needed -->
    <key>com.apple.security.device.camera</key>
    <true/>
    <key>com.apple.security.device.microphone</key>
    <true/>
    
    <!-- Printing -->
    <key>com.apple.security.print</key>
    <true/>
    
    <!-- Required for Hardened Runtime -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    <key>com.apple.security.cs.disable-executable-page-protection</key>
    <true/>
</dict>
</plist>
